/*
 * Copyright (C) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
{
  app: {
    signingConfigs: [
      {
        name: 'default',
        material: {
          certpath: 'C:/Users/<USER>/.ohos/config/openharmony/default_Camera_js_SCqc_m49wX9yKlAhLun_sVVGTLISE4GAvG3X5fv6f3g=.cer',
          storePassword: '0000001AF0AD154BE8BC6BB6956DFF0397DE2BFED807FC91ABB63BC431978735DA78AF6F48CDB726937D',
          keyAlias: 'debugKey',
          keyPassword: '0000001A6253FF04CF9C16F7A25AE90EE84EE97795450BF027C8ADBC8C4B101CCE246DF67ADAFE5F862B',
          profile: 'C:/Users/<USER>/.ohos/config/openharmony/default_Camera_js_SCqc_m49wX9yKlAhLun_sVVGTLISE4GAvG3X5fv6f3g=.p7b',
          signAlg: 'SHA256withECDSA',
          storeFile: 'C:/Users/<USER>/.ohos/config/openharmony/default_Camera_js_SCqc_m49wX9yKlAhLun_sVVGTLISE4GAvG3X5fv6f3g=.p12'
        }
      }
    ],
    products: [
      {
        name: 'default',
        signingConfig: 'default',
        compileSdkVersion: 11,
        compatibleSdkVersion: 11,
        runtimeOS: 'OpenHarmony'
      }
    ],
    buildModeSet: [
      {
        name: 'debug'
      },
      {
        name: 'release'
      }
    ]
  },
  modules: [
    {
      name: 'entry',
      srcPath: './entry',
      targets: [
        {
          name: 'default',
          applyToProducts: ['default']
        }
      ]
    },
    {
      name: 'ijkplayer',
      srcPath: './ijkplayer',
    }
  ]
}
