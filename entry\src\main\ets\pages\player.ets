import { IjkMediaPlayer } from "@ohos/ijkplayer";
import type { OnPreparedListener } from "@ohos/ijkplayer";
import type { OnVideoSizeChangedListener } from "@ohos/ijkplayer";
import type { OnCompletionListener } from "@ohos/ijkplayer";
import type { OnErrorListener } from "@ohos/ijkplayer";
import { LogUtils } from "@ohos/ijkplayer";
import fs from '@ohos.file.fs';
import { ImageProcessingUtils } from '../utils/ImageProcessingUtils';
import { BusinessError } from '@ohos.base';
import util from '@ohos.util';
import buffer from '@ohos.buffer';
import resourceManager from '@ohos.resourceManager';
import { FpsBufferUtils } from '../model/FpsBufferManager';
import { IjkPlayerConfig } from './IjkPlayerConfig';
interface optionsFormat{
  width?: number,
  height?: number,
  pixelFormat?: string,
  framerate?: number
}
@Component
export struct Player {
  @State message: string = 'Stream Player';
  @State isPlaying: boolean = false;
  @State aspRatio: number = 16 / 9; // 默认宽高比
  // @State aspRatio: number = 4 / 3; // 默认宽高比
  @State hasStreamData: boolean = false;
  @State autoPlayEnabled: boolean = true; // 自动播放新帧开关
  private mContext: object | null = null;
  private mIjkMediaPlayer: IjkMediaPlayer | null = null;
  private tempFilePath: string = '';
  private frameQueue: ArrayBuffer[] = []; // 帧队列
  private maxFrames: number = 5; // 最大帧数
  private isPlayerInitialized: boolean = false; // 播放器是否已初始化
  // 循环缓冲区相关属性
  private maxFileSize = 50 * 1024 * 1024; // 50MB限制
  private frameCount = 0;
  // 双文件切换相关属性
  private currentFileIndex = 0;
  private filePaths: string[] = ['', ''];
  private isFileSwitching = false;
  // 方案C：文件大小控制
  private readonly MAX_FILE_SIZE = 20 * 1024 * 1024; // 20MB限制
  // 循环帧缓冲区相关属性
  private readonly BUFFER_FRAMES = 30; // 保持30帧的循环缓冲
  private readonly FRAME_SIZE = 1920 * 1080 * 1.5; // 3110400
  private readonly BUFFER_SIZE = this.BUFFER_FRAMES * this.FRAME_SIZE;
  private currentWriteIndex = 0;
  private isCircularBufferInitialized = false;
  @StorageLink ('FpsCount') @Watch('onFpsCount') PlayerFpsCount:number=0
  private FpsBuffer:ArrayBuffer|undefined|null=null
  async onFpsCount(){
    // console.log('player_player_帧数在变化',this.PlayerFpsCount)
    this.FpsBuffer = FpsBufferUtils.getFpsBuffer();
    // this.FpsBuffer=ImageProcessingUtils.convertYUV420PToNV21(FpsBufferUtils.getFpsBuffer())
    if(this.FpsBuffer && this.FpsBuffer.byteLength > 0){
      console.log('player_player_this.FpsBuffer长度',this.FpsBuffer.byteLength)
      console.log('player_player_this.FpsBuffer长度',this.FpsBuffer.byteLength)
      // 自动处理新的视频帧数据
      await this.processNewFrame(this.FpsBuffer);
      // await this.processNewFrameAppend(this.FpsBuffer);
      // await this.processNewFrameAtomic(this.FpsBuffer);
      // await this.processNewFrameRealTime(this.FpsBuffer);
      // await this.processNewFrameCircular(this.FpsBuffer);

    } else {
      console.log('player_this.FpsBuffer为空||undefined')
    }
  }

  // 处理新的视频帧数据
  private async processNewFrame(frameBuffer: ArrayBuffer) {
    try {
      // 如果自动播放被禁用，直接返回
      if (!this.autoPlayEnabled) {
        return;
      }

      // 如果播放器还没有初始化，先初始化
      if (!this.mIjkMediaPlayer) {
        console.log('player_播放器未初始化，等待初始化完成...');
        return;
      }

      // 首次设置或更新帧数据
      if (!this.hasStreamData) {
        const expectedSize = 1920 * 1080 * 1.5; // YUV420P: 3110400 字节

        if (frameBuffer.byteLength === expectedSize) {
          console.log('player_首次设置YUV流数据...');
          // 先收集几帧数据
          this.frameQueue.push(frameBuffer);
          this.frameQueue.push(frameBuffer); // 重复帧确保连续性
          this.frameQueue.push(frameBuffer);

          const success = await this.setYuvStreamData(frameBuffer);
          if (success) {
            console.log('player_开始播放...');
            this.startPlayWithStream();
          }
        } else {
          console.log(`player_首帧大小不正确: ${frameBuffer.byteLength} 字节, 期望: ${expectedSize} 字节`);
        }
      } else {
        // 更新帧数据
        await this.updateFrameData(frameBuffer);
      }

    } catch (error) {
      console.log('player_处理新帧数据失败:', error);
    }
  }

  // 更新帧数据 - 只更新文件，不操作播放器
  private async updateFrameData(frameBuffer: ArrayBuffer) {
    try {
      const expectedSize = 1920 * 1080 * 1.5; // YUV420P: 3110400 字节

      // 只接受完整的帧数据
      if (frameBuffer.byteLength === expectedSize) {
        this.frameQueue.push(frameBuffer);

        // 限制队列大小
        if (this.frameQueue.length > this.maxFrames) {
          this.frameQueue.shift();
        }

        // 只更新文件，不操作播放器状态
        await this.createContinuousStream();
        // await this.createContinuousStreamNew();
        // await this.createContinuousStream1(); // 原子性文件操作方案

        console.log(`player_接受完整帧: ${frameBuffer.byteLength} 字节`);
      } else {
        console.log(`player_丢弃不完整帧: ${frameBuffer.byteLength} 字节, 期望: ${expectedSize} 字节`);
      }

    } catch (error) {
      console.log('player_更新帧失败:', error);
    }
  }

  // 创建多帧循环视频，修复步长问题
  // 方案一原始帧
  private async createContinuousStream() {
    try {
      if (this.frameQueue.length > 0) {
        // 确保文件路径存在
        if (!this.tempFilePath) {
          await this.createYuvFilePath();
        }

        // 使用最新的帧
        const latestFrame = this.frameQueue[this.frameQueue.length - 1];
        const expectedSize = 1920 * 1080 * 1.5; // 3110400

        // 再次验证帧大小
        if (latestFrame.byteLength !== expectedSize) {
          console.log(`player_跳过不完整帧: ${latestFrame.byteLength} 字节`);
          return;
        }
        // 方案一
        // 简化方案：减少重复次数，避免数据处理错误
        const frameView = new Uint8Array(latestFrame);

        // 创建少量重复帧（重复3次，避免复杂的数据处理）
        const repeatCount = 2;
        const totalSize = latestFrame.byteLength * repeatCount;
        const repeatedBuffer = new ArrayBuffer(totalSize);
        const repeatedView = new Uint8Array(repeatedBuffer);

        // 直接重复写入原始帧
        for (let i = 0; i < repeatCount; i++) {
          // 方案一采用原始帧frameView
          repeatedView.set(frameView, i * latestFrame.byteLength);
        }

        // 写入文件（覆盖原文件）
        const file = fs.openSync(this.tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY | fs.OpenMode.TRUNC);
        fs.writeSync(file.fd, repeatedBuffer);
        // // 一次性写入所有数据，避免分块
        // const bytesWritten = fs.writeSync(file.fd, repeatedBuffer);
        // console.log(`player_写入字节数: ${bytesWritten}, 期望: ${repeatedBuffer.byteLength}`);
        //
        // // 强制刷新到磁盘
        // fs.fsyncSync(file.fd);
        fs.closeSync(file);

        console.log(`player_更新循环视频: ${repeatCount} 帧, 帧大小: ${latestFrame.byteLength}, 总大小: ${totalSize} 字节`);
      }
    } catch (error) {
      console.log('player_创建连续流失败:', error);
    }
  }
  // 方案二原子化


  // 方案二对齐步长帧
  // 创建多帧循环视频，修复步长问题
  private async createContinuousStreamNew() {
    try {
      if (this.frameQueue.length > 0) {
        // 确保文件路径存在
        if (!this.tempFilePath) {
          await this.createYuvFilePath();
        }

        // 使用最新的帧
        const latestFrame = this.frameQueue[this.frameQueue.length - 1];
        const expectedSize = 1920 * 1080 * 1.5; // 3110400

        // 再次验证帧大小
        if (latestFrame.byteLength !== expectedSize) {
          console.log(`player_跳过不完整帧: ${latestFrame.byteLength} 字节`);
          return;
        }

        // 修复步长问题：重新组织YUV数据以匹配1920步长
        // const alignedFrame = this.alignYuvFrame(new Uint8Array(latestFrame));
        const alignedFrame = this.alignYuvFrameNew(new Uint8Array(latestFrame));
        console.log('player_alignedFrame',alignedFrame.length)
        // 创建多帧循环视频（重复30次，形成约1秒的视频）
        const repeatCount = 3;
        const totalSize = alignedFrame.byteLength * repeatCount;
        const repeatedBuffer = new ArrayBuffer(totalSize);
        const repeatedView = new Uint8Array(repeatedBuffer);

        // 重复写入对齐后的帧
        for (let i = 0; i < repeatCount; i++) {
          repeatedView.set(alignedFrame, i * alignedFrame.byteLength);
        }

        // 写入文件（覆盖原文件）
        const file = fs.openSync(this.tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY | fs.OpenMode.TRUNC);
        fs.writeSync(file.fd, repeatedBuffer);
        fs.closeSync(file);

        console.log(`player_更新循环视频: ${repeatCount} 帧, 对齐后大小: ${alignedFrame.byteLength}, 总大小: ${totalSize} 字节`);
      }
    } catch (error) {
      console.log('player_创建连续流失败:', error);
    }
  }
  private alignYuvFrameNew(originalFrame: Uint8Array): Uint8Array {
    const width = 1920;
    const height = 1080;
    const alignedWidth = 1920;

    // 计算各平面大小
    const ySize = width * height;
    const uvSize = (width / 2) * (height / 2);
    const alignedYSize = alignedWidth * height;
    const alignedUVSize = (alignedWidth / 2) * (height / 2);

    // 创建对齐后的缓冲区
    const alignedSize = alignedYSize + alignedUVSize * 2;
    const alignedFrame = new Uint8Array(alignedSize);

    // 复制Y平面（需要逐行复制以处理步长差异）
    for (let row = 0; row < height; row++) {
      const srcOffset = row * width;
      const dstOffset = row * alignedWidth;

      // 复制有效数据
      alignedFrame.set(originalFrame.subarray(srcOffset, srcOffset + width), dstOffset);

      // 🔧 修复：填充剩余区域为0（或复制边缘像素）
      alignedFrame.fill(0, dstOffset + width, dstOffset + alignedWidth);
      // 或者复制边缘像素：
      // const edgePixel = originalFrame[srcOffset + width - 1];
      // alignedFrame.fill(edgePixel, dstOffset + width, dstOffset + alignedWidth);
    }

    // 复制U平面
    const uSrcStart = ySize;
    const uDstStart = alignedYSize;
    for (let row = 0; row < height / 2; row++) {
      const srcOffset = uSrcStart + row * (width / 2);
      const dstOffset = uDstStart + row * (alignedWidth / 2);

      // 复制有效数据
      alignedFrame.set(originalFrame.subarray(srcOffset, srcOffset + width / 2), dstOffset);

      // 🔧 修复：UV平面填充128（中性色）
      alignedFrame.fill(128, dstOffset + width / 2, dstOffset + alignedWidth / 2);
    }

    // 复制V平面
    const vSrcStart = ySize + uvSize;
    const vDstStart = alignedYSize + alignedUVSize;
    for (let row = 0; row < height / 2; row++) {
      const srcOffset = vSrcStart + row * (width / 2);
      const dstOffset = vDstStart + row * (alignedWidth / 2);

      // 复制有效数据
      alignedFrame.set(originalFrame.subarray(srcOffset, srcOffset + width / 2), dstOffset);

      // 🔧 修复：UV平面填充128（中性色）
      alignedFrame.fill(128, dstOffset + width / 2, dstOffset + alignedWidth / 2);
    }

    return alignedFrame;
  }
  // 简化方案：直接使用原始帧，不进行对齐处理
  private alignYuvFrame(originalFrame: Uint8Array): Uint8Array {
    // 既然单帧显示正常，说明原始数据格式是正确的
    // 问题可能出在多帧重复时的数据组织上
    // 直接返回原始帧，不做任何修改
    return originalFrame;
  }

  build() {
    Column() {
      // Text(this.message)
      //   .fontSize(24)
      //   .fontWeight(FontWeight.Bold)
      //   .margin({ bottom: 20 })

      // 视频播放区域
      XComponent({
        id: 'xcomponentId',
        type: 'surface',
        libraryname: 'ijkplayer_napi'
      })
        .onLoad((context) => {
          this.mContext = context || null;
          console.log('XComponent onLoad, context:', this.mContext);
          // 延迟初始化，确保XComponent完全准备好
          setTimeout(() => {
            console.log('开始初始化播放器...');
            this.initPlayer();
          }, 500);
        })
        .onDestroy(() => {
          this.releasePlayer();
        })
        .width('100%')
        .height(200)
        .margin({top:20})
        .aspectRatio(this.aspRatio)
        .backgroundColor(Color.Black)
      // 控制按钮
      Column() {
        Row() {
          Button('加载流数据nnn')
            .onClick(() => {
              this.loadStreamData();
            })
            .margin({ right: 10 })
            .enabled(!this.hasStreamData)

          Button('清除数据')
            .onClick(() => {
              this.clearStreamData();
            })
            .enabled(this.hasStreamData)
        }
        .margin({ bottom: 10 })

        Row() {
          Button(this.autoPlayEnabled ? '关闭自动播放' : '开启自动播放')
            .onClick(() => {
              this.autoPlayEnabled = !this.autoPlayEnabled;
              console.log('player_自动播放状态:', this.autoPlayEnabled ? '开启' : '关闭');
            })
            .margin({ right: 10 })

          Text(`帧数: ${this.PlayerFpsCount}`)
            .fontSize(16)
            .margin({ left: 10 })
        }
        .margin({ bottom: 10 })
        Row() {
          Button(this.isPlaying ? '暂停' : '播放')
            .onClick(() => {
              if (this.isPlaying) {
                this.pausePlay();
              } else {
                this.startPlayWithStream();
              }
            })
            .margin({ right: 10 })
            .enabled(this.hasStreamData)

          Button('停止')
            .onClick(() => {
              this.stopPlay();
            })
            .enabled(this.hasStreamData)
        }
      }
      .margin({ top: 20 })
    }
    .width('100%')
    .height('100%')
    .padding(20)
  }

  private async initPlayer() {
    console.log('initPlayer 开始, mContext:', this.mContext);

    if (!this.mContext) {
      console.log('Context 不可用，无法初始化播放器');
      LogUtils.getInstance().LOGI("Context is null, cannot initialize player");
      return;
    }

    console.log('创建播放器实例...');
    // 设置自定义stride为1280
    // const success = await IjkPlayerConfig.setCustomStride(1280);
    // if(success){
    //   // 验证设置是否成功
    //   const currentStride = await IjkPlayerConfig.getCustomStride();
    //   console.info(`Verified stride: ${currentStride}`);
    // }
    // 创建播放器实例
    this.mIjkMediaPlayer = IjkMediaPlayer.getInstance();

    console.log('设置XComponent上下文...');
    // 设置XComponent上下文
    this.mIjkMediaPlayer.setContext(this.mContext, "xcomponentId");

    console.log('设置调试模式...');
    // 设置调试模式
    this.mIjkMediaPlayer.setDebug(true);

    console.log('初始化配置...');
    // 初始化配置
    this.mIjkMediaPlayer.native_setup();

    console.log('设置播放器选项...');
    // 设置播放器选项
    this.setupPlayerOptions();

    console.log('设置监听器...');
    // 设置监听器
    this.setupListeners();

    console.log('播放器初始化完成');
    LogUtils.getInstance().LOGI("Player initialized successfully");
  }

  private setupPlayerOptions() {
    if (!this.mIjkMediaPlayer) return;
    // 1. 首先设置像素格式和overlay
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "overlay-format", "fcc-i420");
    // this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "overlay-format", "fcc-yv12");
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_CODEC, "skip_frame", "0");
    // 2. 禁用可能影响stride的硬件加速
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "mediacodec", "0");
    // this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max-buffer-size", "3110400"); // 5MB
    // 确保缓冲区足够大//与缓冲区无关
    // this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max-buffer-size", "3110400"); // 5MB
    // 与块大小无关
    // this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "blocksize", "3110400"); // 块大小
    // this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "raw_packet_size", "3110400"); // 明确包
    // 禁用预读优化
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "packet-buffering", "0");
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max_cached_duration", "0");
    // 🔧 关键修复：强制IJKPlayer按完整帧读取
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "fflags", "+ignidx+genpts+nobuffer");
    // this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "buffer_size", "3110400"); // 强制缓冲区大小
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "min-frames", "3"); // 最少1帧
    // YUV 原始视频格式设置
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "video_size", "1920x1080");
    // ======
    // 新增这些参数禁止：
    // this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "align", "1");        // 禁用自动对齐
    // this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_CODEC, "lowres", "0");       // 禁用降采样

    // ======
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "pixel_format", "yuv420p");
    // this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "pixel_format", "nv21");
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "framerate", "25");

    // 修复步长问题 - 强制使用正确的步长
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "STRIDE", "1920");

    // 添加更多格式选项确保正确解析
    // this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "pix_fmt", "yuv420p");
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "format", "rawvideo");

    // 循环播放设置
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "loop", "-1"); // 无限循环
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "autoexit", "0"); // 播放完不退出

    // 使用精确寻帧
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "enable-accurate-seek", "1");

    // 预读数据的缓冲区大小
    // this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max-buffer-size", "102400");

    // 停止预读的最小帧数
    // this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "min-frames", "100");

    // 启动预加载
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "start-on-prepared", "1");

    // 设置无缓冲
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "packet-buffering", "0");

    // 跳帧处理
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "framedrop", "5");

    // 最大缓冲时间3秒
    // this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "max_cached_duration", "3000");

    // 无限制收流
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_PLAYER, "infbuf", "1");

    // 屏幕常亮
    this.mIjkMediaPlayer.setScreenOnWhilePlaying(true);

    // 设置超时
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "timeout", "10000000");
    this.mIjkMediaPlayer.setOption(IjkMediaPlayer.OPT_CATEGORY_FORMAT, "connect_timeout", "10000000");
  }

  private setupListeners() {
    if (!this.mIjkMediaPlayer) return;

    const that = this;

    // 视频尺寸变化监听
    let mOnVideoSizeChangedListener: OnVideoSizeChangedListener = {
      onVideoSizeChanged(width: number, height: number, sar_num: number, sar_den: number) {
        that.aspRatio = width / height;
        LogUtils.getInstance().LOGI(`Video size changed: ${width}x${height}, aspect ratio: ${that.aspRatio}`);
      }
    };
    this.mIjkMediaPlayer.setOnVideoSizeChangedListener(mOnVideoSizeChangedListener);

    // 准备完成监听
    let mOnPreparedListener: OnPreparedListener = {
      onPrepared() {
        LogUtils.getInstance().LOGI("Player prepared, ready to play");
        that.mIjkMediaPlayer?.start();
        that.isPlaying = true;
      }
    };
    this.mIjkMediaPlayer.setOnPreparedListener(mOnPreparedListener);

    // 播放完成监听
    let mOnCompletionListener: OnCompletionListener = {
      onCompletion() {
        LogUtils.getInstance().LOGI("Playback completed");
        that.isPlaying = false;
      }
    };
    this.mIjkMediaPlayer.setOnCompletionListener(mOnCompletionListener);

    // 错误监听
    let mOnErrorListener: OnErrorListener = {
      onError(what: number, extra: number) {
        LogUtils.getInstance().LOGI(`Player error: what=${what}, extra=${extra}`);
        that.isPlaying = false;
      }
    };
    this.mIjkMediaPlayer.setOnErrorListener(mOnErrorListener);

    // 设置消息监听器
    this.mIjkMediaPlayer.setMessageListener();
  }

  private startPlay() {
    if (!this.mIjkMediaPlayer) {
      LogUtils.getInstance().LOGI("Player not initialized");
      return;
    }

    if (this.isPlaying) {
      LogUtils.getInstance().LOGI("Already playing");
      return;
    }

    this.mIjkMediaPlayer.start();
    this.isPlaying = true;
    LogUtils.getInstance().LOGI("Started playback");
  }

  private pausePlay() {
    if (!this.mIjkMediaPlayer) {
      LogUtils.getInstance().LOGI("Player not initialized");
      return;
    }

    if (!this.isPlaying) {
      LogUtils.getInstance().LOGI("Already paused");
      return;
    }

    this.mIjkMediaPlayer.pause();
    this.isPlaying = false;
    LogUtils.getInstance().LOGI("Paused playback");
  }

  private stopPlay() {
    if (!this.mIjkMediaPlayer) return;
    this.mIjkMediaPlayer.stop();
    this.isPlaying = false;
  }

  private releasePlayer() {
    if (this.mIjkMediaPlayer) {
      this.mIjkMediaPlayer.release();
      this.mIjkMediaPlayer = null;
      this.isPlaying = false;
    }
  }

  // 手动加载当前的FpsBuffer流数据
  private async loadStreamData() {
    try {
      if (this.FpsBuffer && this.FpsBuffer.byteLength > 0) {
        console.log('player_手动加载FpsBuffer数据...');
        const success = await this.setYuvStreamData(this.FpsBuffer);
        if (success) {
          LogUtils.getInstance().LOGI("Stream data loaded successfully");
          this.message = 'Stream Player - FpsBuffer数据已加载';
        } else {
          throw new Error('设置YUV流数据失败');
        }
      } else {
        // 如果没有FpsBuffer，使用测试数据
        console.log('player_没有FpsBuffer数据，使用测试视频...');
        await this.setupTestVideoSource();
        this.hasStreamData = true;
        this.message = 'Stream Player - 测试数据已加载';
      }
    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to load stream data: ${error}`);
      this.message = 'Stream Player - 数据加载失败';
    }
  }

  // 创建测试流数据（实际应用中替换为真实的ArrayBuffer数据）
  private async createTestStreamData() {
    // 为了演示YUV播放，我们使用YUV文件
    this.setupYuvVideoSource();
  }

  // 专门处理YUV文件的方法
  private async setupYuvVideoSource() {
    try {
      // 获取YUV文件数据
      const rawFileData = await getContext(this).resourceManager.getRawFileContent('065(49).yuv');

      // 获取应用缓存目录
      const context = getContext(this);
      const cacheDir = context.cacheDir;
      const tempFilePath = `${cacheDir}/065(49).yuv`;

      // 写入临时文件
      const file = fs.openSync(tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY);
      fs.writeSync(file.fd, rawFileData.buffer);
      fs.closeSync(file);

      console.log('player_tempFilePath', tempFilePath);

      // 设置数据源
      if (this.mIjkMediaPlayer) {
        this.mIjkMediaPlayer.setDataSource(tempFilePath);
        LogUtils.getInstance().LOGI(`Set YUV video source: ${tempFilePath}`);
      }

      // 保存临时文件路径，用于清理
      this.tempFilePath = tempFilePath;

    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to setup YUV video source: ${error}`);
    }
  }

  // 处理真实的ArrayBuffer流数据的方法
  public async setStreamData(buffer: ArrayBuffer): Promise<boolean> {
    try {
      if (!buffer || buffer.byteLength === 0) {
        LogUtils.getInstance().LOGI("Invalid ArrayBuffer data");
        return false;
      }

      // 将ArrayBuffer写入临时文件
      const filePath = await this.writeArrayBufferToFile(buffer);

      // 设置数据源
      if (this.mIjkMediaPlayer) {
        this.mIjkMediaPlayer.setDataSource(filePath);
        LogUtils.getInstance().LOGI(`Set stream data source: ${filePath}`);
      }

      this.hasStreamData = true;
      this.message = `Stream Player - 已加载 ${buffer.byteLength} 字节数据`;

      return true;
    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to set stream data: ${error}`);
      return false;
    }
  }

  // 设置测试视频源（用于演示）
  private async setupTestVideoSource() {
    try {
      await this.copyRawFileToTemp();
      // // // 方法1：获取 rawfile 的文件描述符
      // const rawFd = await getContext(this).resourceManager.getRawFd('videoTest.mp4');
      // console.log('player_rawFd', rawFd);
      //
      // // 构造文件描述符路径
      // const fdPath = `fd://${rawFd.fd}:${rawFd.offset}:${rawFd.length}`;
      //
      // if (this.mIjkMediaPlayer) {
      //   this.mIjkMediaPlayer.setDataSource(fdPath);
      //   LogUtils.getInstance().LOGI(`Set test video source: ${fdPath}`);
      // }
    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to setup video source: ${error}`);
      // 备用方案：复制到临时文件
      // await this.copyRawFileToTemp();
    }
  }

  // 备用方案：将 rawfile 复制到临时文件
  private async copyRawFileToTemp(): Promise<void> {
    try {
      // 获取 rawfile 数据
      const rawFileData = await getContext(this).resourceManager.getRawFileContent('videoTest.mp4');
      // const rawFileData = await getContext(this).resourceManager.getRawFileContent('065(49).yuv');

      // 获取应用缓存目录
      const context = getContext(this);
      const cacheDir = context.cacheDir;
      const tempFilePath = `${cacheDir}/temp_video.mp4`;
      // const tempFilePath = `${cacheDir}/065(49).yuv`;

      // 写入临时文件
      const file = fs.openSync(tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY);
      fs.writeSync(file.fd, rawFileData.buffer);
      fs.closeSync(file);
      console.log('player_tempFilePath',tempFilePath)
      // 设置数据源
      if (this.mIjkMediaPlayer) {
        this.mIjkMediaPlayer.setDataSource(tempFilePath);
        LogUtils.getInstance().LOGI(`Set test video source (temp file): ${tempFilePath}`);
      }

      // 保存临时文件路径，用于清理
      this.tempFilePath = tempFilePath;

    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to copy rawfile to temp: ${error}`);
    }
  }

  // 处理YUV数据 - 使用连续流
  public async setYuvStreamData(buffer: ArrayBuffer): Promise<boolean> {
    try {
      if (!buffer || buffer.byteLength === 0) return false;

      // 创建文件路径
      await this.createYuvFilePath();

      // 创建连续流文件
      await this.createContinuousStream();
      // await this.createContinuousStreamNew();

      if (this.mIjkMediaPlayer && this.tempFilePath && !this.isPlayerInitialized) {
        this.mIjkMediaPlayer.setDataSource(this.tempFilePath);
        this.isPlayerInitialized = true;
        console.log('player_首次设置数据源完成');
      }

      this.hasStreamData = true;
      this.message = `Stream Player - 已加载连续流 ${this.frameQueue.length} 帧`;
      return true;
    } catch (error) {
      console.log('setYuvStreamData失败:', error);
      return false;
    }
  }

  // 创建YUV临时文件路径
  private async createYuvFilePath(): Promise<string> {
    try {
      const context = getContext(this);
      const cacheDir = context.cacheDir;

      // 确保缓存目录存在
      if (!fs.accessSync(cacheDir)) {
        fs.mkdirSync(cacheDir, true);
      }

      const fileName = `yuv_stream.yuv`;
      const tempFilePath = `${cacheDir}/${fileName}`;
      this.tempFilePath = tempFilePath;
      return tempFilePath;
    } catch (error) {
      console.log('创建YUV文件路径失败:', error);
      throw new Error('创建YUV文件路径失败');
    }
  }
  // 创建YUV原子临时文件路径
  private async createYuvFilePath1(): Promise<string> {
    try {
      const context = getContext(this);
      const cacheDir = context.cacheDir;

      // 确保缓存目录存在
      if (!fs.accessSync(cacheDir)) {
        fs.mkdirSync(cacheDir, true);
      }

      const fileName = `yuv_stream.yuv`;
      const tempFilePath = `${cacheDir}/${fileName}`;
      this.tempFilePath = tempFilePath;
      return tempFilePath;
    } catch (error) {
      console.log('创建YUV文件路径失败:', error);
      throw new Error('创建YUV文件路径失败');
    }
  }

  // 将ArrayBuffer写入临时文件的方法（实际应用中使用）
  private async writeArrayBufferToFile(buffer: ArrayBuffer): Promise<string> {
    try {
      // 获取应用缓存目录
      const context = getContext(this);
      const cacheDir = context.cacheDir;
      const fileName = `stream_${Date.now()}.mp4`;
      this.tempFilePath = `${cacheDir}/${fileName}`;

      // 将ArrayBuffer写入文件
      const file = fs.openSync(this.tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY);
      const writeLen = fs.writeSync(file.fd, buffer);
      fs.closeSync(file);

      LogUtils.getInstance().LOGI(`Written ${writeLen} bytes to ${this.tempFilePath}`);
      return this.tempFilePath;
    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to write ArrayBuffer to file: ${error}`);
      throw new Error(`Failed to write ArrayBuffer to file: ${error}`);
    }
  }

  // 使用流数据开始播放
  private startPlayWithStream() {
    if (!this.hasStreamData) {
      LogUtils.getInstance().LOGI("No stream data available");
      return;
    }

    if (!this.mIjkMediaPlayer) {
      LogUtils.getInstance().LOGI("Player not initialized");
      return;
    }

    try {
      // 准备播放
      this.mIjkMediaPlayer.prepareAsync();
      LogUtils.getInstance().LOGI("Started preparing stream for playback");
    } catch (error) {
      LogUtils.getInstance().LOGI(`Failed to start playback: ${error}`);
    }
  }

  // 新的帧处理方法 - 持续追加写入，不覆盖
  public async processNewFrameAppend(frameBuffer: ArrayBuffer): Promise<void> {
    try {
      const expectedSize = 1920 * 1080 * 1.5; // YUV420P: 3110400 字节

      // 验证帧大小
      if (frameBuffer.byteLength !== expectedSize) {
        console.log(`processNewFrameAppend_丢弃不完整帧: ${frameBuffer.byteLength} 字节, 期望: ${expectedSize} 字节`);
        return;
      }

      // 确保播放器已初始化
      if (!this.mIjkMediaPlayer) {
        console.log('processNewFrameAppend_播放器未初始化');
        return;
      }

      // 首次处理：创建文件并设置数据源
      if (!this.hasStreamData) {
        await this.initializeStreamFile(frameBuffer);
        return;
      }

      // 后续处理：追加写入新帧
      // 方案A：循环缓冲区
      await this.appendFrameToFile(frameBuffer);

      // 方案B：双文件切换
      // await this.appendFrameToFileB(frameBuffer);

    } catch (error) {
      console.log('processNewFrameAppend_处理失败:', error);
    }
  }

  // 初始化流文件
  private async initializeStreamFile(frameBuffer: ArrayBuffer): Promise<void> {
    try {

      // 初始化双文件系统
      await this.initializeDualFileSystem();

      // 创建文件路径（兼容原有逻辑）
      await this.createYuvFilePath();

      // 写入初始帧（可以重复几次确保有足够数据）
      const initialFrameCount = 2; // 初始写入3帧
      const initialSize = frameBuffer.byteLength * initialFrameCount;
      const initialBuffer = new ArrayBuffer(initialSize);
      const initialView = new Uint8Array(initialBuffer);
      const frameView = new Uint8Array(frameBuffer);

      // 重复写入初始帧
      for (let i = 0; i < initialFrameCount; i++) {
        initialView.set(frameView, i * frameBuffer.byteLength);
      }

      // 写入文件
      const file = fs.openSync(this.tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY);
      fs.writeSync(file.fd, initialBuffer);
      fs.fsyncSync(file.fd);
      fs.closeSync(file);
      if (this.mIjkMediaPlayer && this.tempFilePath && !this.isPlayerInitialized) {
        this.mIjkMediaPlayer.setDataSource(this.tempFilePath);
        this.isPlayerInitialized = true;
        console.log('player_首次设置数据源完成');
      }
      // 设置数据源并开始播放
      // this.mIjkMediaPlayer.setDataSource(this.tempFilePath);
      this.hasStreamData = true;

      console.log(`processNewFrameAppend_初始化完成: ${initialFrameCount} 帧, 文件: ${this.tempFilePath}`);

      // 开始播放
      setTimeout(() => {
        this.startPlayWithStream();
      }, 100);

    } catch (error) {
      console.log('processNewFrameAppend_初始化失败:', error);
    }
  }

  // 追加帧到文件 - 循环缓冲区方案
  private async appendFrameToFile(frameBuffer: ArrayBuffer): Promise<void> {
    try {
      // 检查文件大小，超过限制时重新开始
      const fileStats = fs.statSync(this.tempFilePath);
      if (fileStats.size > this.maxFileSize) {
        console.log(`文件大小超限(${fileStats.size} > ${this.maxFileSize})，重新开始写入`);
        await this.resetStreamFile(frameBuffer);
        return;
      }

      // 正常追加
      const file = fs.openSync(this.tempFilePath, fs.OpenMode.WRITE_ONLY | fs.OpenMode.APPEND);
      fs.writeSync(file.fd, frameBuffer);
      fs.fsyncSync(file.fd); // 确保数据写入磁盘
      fs.closeSync(file);

      this.frameCount++;
      const newFileSize = fileStats.size + frameBuffer.byteLength;
      console.log(`追加第${this.frameCount}帧，文件大小: ${newFileSize} 字节 (${(newFileSize / 1024 / 1024).toFixed(2)}MB)`);

    } catch (error) {
      console.log('processNewFrameAppend_追加帧失败:', error);
    }
  }

  // 重置流文件
  private async resetStreamFile(frameBuffer: ArrayBuffer): Promise<void> {
    try {
      console.log('开始重置流文件...');

      // 重新创建文件，从新帧开始（写入3帧确保连续性）
      const initialFrameCount = 3;
      const initialSize = frameBuffer.byteLength * initialFrameCount;
      const initialBuffer = new ArrayBuffer(initialSize);
      const initialView = new Uint8Array(initialBuffer);
      const frameView = new Uint8Array(frameBuffer);

      // 重复写入当前帧
      for (let i = 0; i < initialFrameCount; i++) {
        initialView.set(frameView, i * frameBuffer.byteLength);
      }

      // 覆盖文件
      const file = fs.openSync(this.tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY | fs.OpenMode.TRUNC);
      fs.writeSync(file.fd, initialBuffer);
      fs.fsyncSync(file.fd);
      fs.closeSync(file);

      this.frameCount = initialFrameCount;
      console.log(`重置流文件完成，写入${initialFrameCount}帧，大小: ${initialSize} 字节`);

    } catch (error) {
      console.log('重置流文件失败:', error);
    }
  }

  // 方案B：双文件切换的追加帧方法
  private async appendFrameToFileB(frameBuffer: ArrayBuffer): Promise<void> {
    try {
      // 如果正在切换文件，等待完成
      if (this.isFileSwitching) {
        console.log('正在切换文件，跳过当前帧');
        return;
      }

      const currentPath = this.filePaths[this.currentFileIndex];

      // 检查当前文件大小
      const fileStats = fs.statSync(currentPath);
      if (fileStats.size > this.maxFileSize) {
        console.log(`文件${this.currentFileIndex}大小超限(${fileStats.size} > ${this.maxFileSize})，切换到另一个文件`);
        await this.switchToNextFile(frameBuffer);
        return;
      }

      // 正常追加到当前文件
      const file = fs.openSync(currentPath, fs.OpenMode.WRITE_ONLY | fs.OpenMode.APPEND);
      fs.writeSync(file.fd, frameBuffer);
      fs.fsyncSync(file.fd);
      fs.closeSync(file);

      this.frameCount++;
      const newFileSize = fileStats.size + frameBuffer.byteLength;
      console.log(`文件${this.currentFileIndex}_追加第${this.frameCount}帧，大小: ${(newFileSize / 1024 / 1024).toFixed(2)}MB`);

    } catch (error) {
      console.log('appendFrameToFileB_失败:', error);
    }
  }

  // 切换到下一个文件
  private async switchToNextFile(frameBuffer: ArrayBuffer): Promise<void> {
    try {
      this.isFileSwitching = true;

      // 切换到另一个文件
      const nextFileIndex = 1 - this.currentFileIndex;
      const nextPath = this.filePaths[nextFileIndex];

      console.log(`开始切换：文件${this.currentFileIndex} -> 文件${nextFileIndex}`);

      // 初始化新文件（写入3帧确保连续性）
      await this.initializeNewFile(nextPath, frameBuffer);

      // 更新播放器数据源到新文件
      if (this.mIjkMediaPlayer && this.isPlaying) {
        console.log(`更新播放器数据源到: ${nextPath}`);
        this.mIjkMediaPlayer.setDataSource(nextPath);
      }

      // 切换当前文件索引
      this.currentFileIndex = nextFileIndex;
      this.frameCount = 3; // 重置帧计数（已写入3帧）

      console.log(`文件切换完成，当前使用文件${this.currentFileIndex}`);

    } catch (error) {
      console.log('切换文件失败:', error);
    } finally {
      this.isFileSwitching = false;
    }
  }

  // 初始化新文件
  private async initializeNewFile(filePath: string, frameBuffer: ArrayBuffer): Promise<void> {
    try {
      const initialFrameCount = 3;
      const initialSize = frameBuffer.byteLength * initialFrameCount;
      const initialBuffer = new ArrayBuffer(initialSize);
      const initialView = new Uint8Array(initialBuffer);
      const frameView = new Uint8Array(frameBuffer);

      // 重复写入当前帧
      for (let i = 0; i < initialFrameCount; i++) {
        initialView.set(frameView, i * frameBuffer.byteLength);
      }

      // 创建/覆盖文件
      const file = fs.openSync(filePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY | fs.OpenMode.TRUNC);
      fs.writeSync(file.fd, initialBuffer);
      fs.fsyncSync(file.fd);
      fs.closeSync(file);

      console.log(`初始化新文件完成: ${filePath}，写入${initialFrameCount}帧`);

    } catch (error) {
      console.log('初始化新文件失败:', error);
    }
  }

  // 初始化双文件系统
  private async initializeDualFileSystem(): Promise<void> {
    try {
      const context = getContext(this);
      const cacheDir = context.cacheDir;
      // 确保缓存目录存在
      if (!fs.accessSync(cacheDir)) {
        fs.mkdirSync(cacheDir, true);
      }
      // 创建两个文件路径
      this.filePaths[0] = `${cacheDir}/yuv_stream_0.yuv`;
      this.filePaths[1] = `${cacheDir}/yuv_stream_1.yuv`;

      console.log('双文件系统路径:');
      console.log(`文件0: ${this.filePaths[0]}`);
      console.log(`文件1: ${this.filePaths[1]}`);

      // 设置当前使用的文件路径（兼容现有代码）
      this.tempFilePath = this.filePaths[0];

    } catch (error) {
      console.log('初始化双文件系统失败:', error);
    }
  }

  // 方案三：原子性追加帧 - 临时文件 + 原子性合并 + 文件大小控制
  private async atomicAppendFrame(frameBuffer: ArrayBuffer): Promise<void> {
    try {
      // 验证帧大小
      const expectedSize = 1920 * 1080 * 1.5; // 3110400
      if (frameBuffer.byteLength !== expectedSize) {
        throw new Error(`帧大小不正确: ${frameBuffer.byteLength}, 期望: ${expectedSize}`);
      }

      // 检查文件大小，超过限制时重新初始化
      const fileStats = fs.statSync(this.tempFilePath);
      if (fileStats.size > this.MAX_FILE_SIZE) {
        console.log(`文件大小超限(${(fileStats.size / 1024 / 1024).toFixed(2)}MB > ${(this.MAX_FILE_SIZE / 1024 / 1024).toFixed(2)}MB)，重新初始化`);
        await this.reinitializeFile(frameBuffer);
        return;
      }

      // 执行原子性追加
      await this.performAtomicAppend(frameBuffer);

    } catch (error) {
      console.log('原子性追加失败:', error);

      throw new Error(error);
    }
  }

  // 执行原子性追加的核心逻辑
  private async performAtomicAppend(frameBuffer: ArrayBuffer): Promise<void> {
    const tempPath = this.tempFilePath + '.tmp';

    try {

      // 1. 写入临时文件（原子性准备）
      const tempFile = fs.openSync(tempPath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY | fs.OpenMode.TRUNC);
      const bytesWritten = fs.writeSync(tempFile.fd, frameBuffer);

      if (bytesWritten !== frameBuffer.byteLength) {
        fs.closeSync(tempFile);
        throw new Error(`临时文件写入不完整: ${bytesWritten}/${frameBuffer.byteLength}`);
      }

      fs.fsyncSync(tempFile.fd); // 强制刷新到磁盘
      fs.closeSync(tempFile);

      // 2. 读取临时文件（确保数据完整性）
      const readFile = fs.openSync(tempPath, fs.OpenMode.READ_ONLY);
      const tempData = new ArrayBuffer(frameBuffer.byteLength);
      const bytesRead = fs.readSync(readFile.fd, tempData);
      fs.closeSync(readFile);

      if (bytesRead !== frameBuffer.byteLength) {
        throw new Error(`临时文件读取不完整: ${bytesRead}/${frameBuffer.byteLength}`);
      }

      // 3. 原子性追加到主文件
      const mainFile = fs.openSync(this.tempFilePath, fs.OpenMode.WRITE_ONLY | fs.OpenMode.APPEND);
      // const mainFile = fs.openSync(this.tempFilePath, fs.OpenMode.WRITE_ONLY | fs.OpenMode.TRUNC);
      const mainBytesWritten = fs.writeSync(mainFile.fd, tempData);

      if (mainBytesWritten !== frameBuffer.byteLength) {
        fs.closeSync(mainFile);
        throw new Error(`主文件写入不完整: ${mainBytesWritten}/${frameBuffer.byteLength}`);
      }

      fs.fsyncSync(mainFile.fd); // 确保数据到达磁盘
      fs.closeSync(mainFile);

      // 4. 清理临时文件
      fs.unlinkSync(tempPath);

      console.log(`原子性追加完成: ${frameBuffer.byteLength} 字节`);

    } catch (error) {
      console.log('原子性追加失败:', error);
      // 清理临时文件
      try {
        if (fs.accessSync(tempPath)) {
          fs.unlinkSync(tempPath);
        }
      } catch (cleanupError) {
        console.log('清理临时文件失败:', cleanupError);
      }
      let error1=new Error(error)
      throw error1; // 重新抛出错误
    }
  }

  // 重新初始化文件 - 控制文件大小，避免无限增长
  private async reinitializeFile(frameBuffer: ArrayBuffer): Promise<void> {
    try {
      console.log('开始重新初始化文件...');

      // 重新创建文件，写入3帧确保连续性
      const initialFrameCount = 3;
      const initialSize = frameBuffer.byteLength * initialFrameCount;
      const initialBuffer = new ArrayBuffer(initialSize);
      const initialView = new Uint8Array(initialBuffer);
      const frameView = new Uint8Array(frameBuffer);

      // 重复写入当前帧
      for (let i = 0; i < initialFrameCount; i++) {
        initialView.set(frameView, i * frameBuffer.byteLength);
      }

      // 原子性重新创建文件
      const file = fs.openSync(this.tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY | fs.OpenMode.TRUNC);
      const bytesWritten = fs.writeSync(file.fd, initialBuffer);

      if (bytesWritten !== initialSize) {
        fs.closeSync(file);
        throw new Error(`重新初始化写入不完整: ${bytesWritten}/${initialSize}`);
      }

      fs.fsyncSync(file.fd);
      fs.closeSync(file);

      console.log(`重新初始化文件完成: ${initialFrameCount} 帧, 大小: ${(initialSize / 1024 / 1024).toFixed(2)}MB`);

    } catch (error) {
      console.log('重新初始化文件失败:', error);
      throw new Error(error);
    }
  }

  // 实时流方案：单帧覆盖 - 适合实时相机流播放
  public async processNewFrameRealTime(frameBuffer: ArrayBuffer): Promise<void> {
    try {
      const expectedSize = 1920 * 1080 * 1.5; // YUV420P: 3110400 字节

      // 验证帧大小
      if (frameBuffer.byteLength !== expectedSize) {
        console.log(`processNewFrameRealTime_丢弃不完整帧: ${frameBuffer.byteLength} 字节, 期望: ${expectedSize} 字节`);
        return;
      }

      // 确保播放器已初始化
      if (!this.mIjkMediaPlayer) {
        console.log('processNewFrameRealTime_播放器未初始化');
        return;
      }

      // 首次处理：初始化实时流
      if (!this.hasStreamData) {
        await this.initializeRealTimeStream(frameBuffer);
        return;
      }

      // 后续处理：实时更新帧
      await this.updateRealTimeFrame(frameBuffer);

    } catch (error) {
      console.log('processNewFrameRealTime_处理失败:', error);
    }
  }

  // 初始化实时流 - 创建固定大小的单帧文件
  private async initializeRealTimeStream(frameBuffer: ArrayBuffer): Promise<void> {
    try {
      // 创建文件路径
      await this.createYuvFilePath();

      // 直接写入单帧数据
      const file = fs.openSync(this.tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY);
      const bytesWritten = fs.writeSync(file.fd, frameBuffer);

      if (bytesWritten !== frameBuffer.byteLength) {
        fs.closeSync(file);
        throw new Error(`初始化写入不完整: ${bytesWritten}/${frameBuffer.byteLength}`);
      }

      fs.fsyncSync(file.fd);
      fs.closeSync(file);
      if(this.mIjkMediaPlayer){
        // 设置数据源并开始播放
        this.mIjkMediaPlayer.setDataSource(this.tempFilePath);
      }


      this.hasStreamData = true;

      console.log(`processNewFrameRealTime_初始化完成: 单帧文件, 大小: ${frameBuffer.byteLength} 字节`);

      // 开始播放
      setTimeout(() => {
        this.startPlayWithStream();
      }, 100);

    } catch (error) {
      console.log('processNewFrameRealTime_初始化失败:', error);
      throw new Error(error);
    }
  }

  // 实时更新帧 - 直接覆盖文件，保持文件大小不变
  private async updateRealTimeFrame(frameBuffer: ArrayBuffer): Promise<void> {
    try {
      // 直接覆盖，保持文件大小固定为一帧
      const file = fs.openSync(this.tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY | fs.OpenMode.TRUNC);
      const bytesWritten = fs.writeSync(file.fd, frameBuffer);

      if (bytesWritten !== frameBuffer.byteLength) {
        fs.closeSync(file);
        throw new Error(`实时更新写入不完整: ${bytesWritten}/${frameBuffer.byteLength}`);
      }

      fs.fsyncSync(file.fd); // 确保数据立即写入磁盘
      fs.closeSync(file);

      console.log(`实时帧更新完成: ${frameBuffer.byteLength} 字节`);

    } catch (error) {
      console.log('实时帧更新失败:', error);
      throw new Error(error);
    }
  }

  // 循环帧缓冲区方案 - 适合实时流播放，解决rawvideo连续性需求
  public async processNewFrameCircular(frameBuffer: ArrayBuffer): Promise<void> {
    try {
      const expectedSize = 1920 * 1080 * 1.5; // YUV420P: 3110400 字节

      // 验证帧大小
      if (frameBuffer.byteLength !== expectedSize) {
        console.log(`processNewFrameCircular_丢弃不完整帧: ${frameBuffer.byteLength} 字节, 期望: ${expectedSize} 字节`);
        return;
      }

      // 确保播放器已初始化
      if (!this.mIjkMediaPlayer) {
        console.log('processNewFrameCircular_播放器未初始化');
        return;
      }

      // 首次处理：初始化循环缓冲区
      if (!this.isCircularBufferInitialized) {
        await this.initializeCircularBuffer(frameBuffer);
        return;
      }

      // 后续处理：更新循环缓冲区中的帧
      await this.updateCircularFrame(frameBuffer);

    } catch (error) {
      console.log('processNewFrameCircular_处理失败:', error);
    }
  }

  // 初始化循环缓冲区 - 创建固定大小的多帧文件
  private async initializeCircularBuffer(frameBuffer: ArrayBuffer): Promise<void> {
    try {
      // 创建文件路径
      await this.createYuvFilePath();

      // 创建固定大小的缓冲区，填充相同帧数据
      const buffer = new ArrayBuffer(this.BUFFER_SIZE);
      const bufferView = new Uint8Array(buffer);
      const frameView = new Uint8Array(frameBuffer);

      // 填充30帧相同数据，确保rawvideo有足够的连续数据
      for (let i = 0; i < this.BUFFER_FRAMES; i++) {
        bufferView.set(frameView, i * this.FRAME_SIZE);
      }

      // 一次性写入完整缓冲区
      const file = fs.openSync(this.tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY);
      const bytesWritten = fs.writeSync(file.fd, buffer);

      if (bytesWritten !== this.BUFFER_SIZE) {
        fs.closeSync(file);
        throw new Error(`循环缓冲区初始化写入不完整: ${bytesWritten}/${this.BUFFER_SIZE}`);
      }

      fs.fsyncSync(file.fd);
      fs.closeSync(file);

      // 设置数据源并开始播放
      if(this.mIjkMediaPlayer){
        this.mIjkMediaPlayer.setDataSource(this.tempFilePath);
      }
      this.hasStreamData = true;
      this.isCircularBufferInitialized = true;
      this.currentWriteIndex = 0;

      console.log(`processNewFrameCircular_初始化完成: ${this.BUFFER_FRAMES}帧, 总大小: ${(this.BUFFER_SIZE / 1024 / 1024).toFixed(2)}MB`);

      // 开始播放
      setTimeout(() => {
        this.startPlayWithStream();
      }, 100);

    } catch (error) {
      console.log('processNewFrameCircular_初始化失败:', error);
      throw new Error(error);
    }
  }

  // 更新循环缓冲区中的帧 - 定位写入，避免竞态条件
  private async updateCircularFrame(frameBuffer: ArrayBuffer): Promise<void> {
    try {
      // 计算当前帧在文件中的写入位置
      const writeOffset = this.currentWriteIndex * this.FRAME_SIZE;

      console.log(`更新循环帧${this.currentWriteIndex}，文件偏移: ${writeOffset}`);

      // 方案：读取整个文件，修改指定帧，重新写入
      // 1. 读取整个缓冲区文件
      const readFile = fs.openSync(this.tempFilePath, fs.OpenMode.READ_ONLY);
      const entireBuffer = new ArrayBuffer(this.BUFFER_SIZE);
      const bytesRead = fs.readSync(readFile.fd, entireBuffer);
      fs.closeSync(readFile);

      if (bytesRead !== this.BUFFER_SIZE) {
        throw new Error(`读取缓冲区文件不完整: ${bytesRead}/${this.BUFFER_SIZE}`);
      }

      // 2. 修改指定位置的帧数据
      const entireView = new Uint8Array(entireBuffer);
      const frameView = new Uint8Array(frameBuffer);

      // 将新帧数据复制到指定位置
      entireView.set(frameView, writeOffset);

      // 3. 原子性重写整个文件
      const tempPath = this.tempFilePath + '.updating';

      // 写入临时文件
      const tempFile = fs.openSync(tempPath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY);
      const bytesWritten = fs.writeSync(tempFile.fd, entireBuffer);

      if (bytesWritten !== this.BUFFER_SIZE) {
        fs.closeSync(tempFile);
        fs.unlinkSync(tempPath);
        throw new Error(`临时文件写入不完整: ${bytesWritten}/${this.BUFFER_SIZE}`);
      }

      fs.fsyncSync(tempFile.fd);
      fs.closeSync(tempFile);

      // 4. 原子性替换原文件
      fs.renameSync(tempPath, this.tempFilePath);

      // 更新写入索引（循环）
      this.currentWriteIndex = (this.currentWriteIndex + 1) % this.BUFFER_FRAMES;

      console.log(`循环帧更新完成，下一个索引: ${this.currentWriteIndex}`);

    } catch (error) {
      console.log('循环帧更新失败:', error);
      throw new Error(error) ;
    }
  }

  // 新的帧处理方法 - 使用原子性追加
  public async processNewFrameAtomic(frameBuffer: ArrayBuffer): Promise<void> {
    try {
      const expectedSize = 1920 * 1080 * 1.5; // YUV420P: 3110400 字节

      // 验证帧大小
      if (frameBuffer.byteLength !== expectedSize) {
        console.log(`processNewFrameAtomic_丢弃不完整帧: ${frameBuffer.byteLength} 字节, 期望: ${expectedSize} 字节`);
        return;
      }

      // 确保播放器已初始化
      if (!this.mIjkMediaPlayer) {
        console.log('processNewFrameAtomic_播放器未初始化');
        return;
      }

      // 首次处理：创建文件并设置数据源
      if (!this.hasStreamData) {
        await this.initializeStreamFileAtomic(frameBuffer);
        return;
      }

      // 后续处理：原子性追加新帧
      await this.atomicAppendFrame(frameBuffer);

    } catch (error) {
      console.log('processNewFrameAtomic_处理失败:', error);
    }
  }

  // 原子性初始化流文件
  private async initializeStreamFileAtomic(frameBuffer: ArrayBuffer): Promise<void> {
    try {
      // 创建文件路径
      await this.createYuvFilePath();

      // 写入初始帧（使用原子性方法写入3帧确保有足够数据）
      const initialFrameCount = 3;
      const initialSize = frameBuffer.byteLength * initialFrameCount;
      const initialBuffer = new ArrayBuffer(initialSize);
      const initialView = new Uint8Array(initialBuffer);
      const frameView = new Uint8Array(frameBuffer);

      // 重复写入初始帧
      for (let i = 0; i < initialFrameCount; i++) {
        initialView.set(frameView, i * frameBuffer.byteLength);
      }

      // 原子性写入初始数据
      const file = fs.openSync(this.tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY);
      const bytesWritten = fs.writeSync(file.fd, initialBuffer);

      if (bytesWritten !== initialSize) {
        fs.closeSync(file);
        throw new Error(`初始化写入不完整: ${bytesWritten}/${initialSize}`);
      }

      fs.fsyncSync(file.fd);
      fs.closeSync(file);
      if (this.mIjkMediaPlayer) {
        this.mIjkMediaPlayer.setDataSource(this.tempFilePath)
        // LogUtils.getInstance().LOGI(`Set YUV video source: ${tempFilePath}`);
      }
      // 设置数据源并开始播放
      // this.mIjkMediaPlayer.setDataSource(this.tempFilePath);
      this.hasStreamData = true;

      console.log(`processNewFrameAtomic_初始化完成: ${initialFrameCount} 帧, 文件: ${this.tempFilePath}`);

      // 开始播放
      setTimeout(() => {
        this.startPlayWithStream();
      }, 100);

    } catch (error) {
      console.log('processNewFrameAtomic_初始化失败:', error);

      throw new Error(error);
    }
  }

  // 原子性文件操作方案 - 解决竞态条件问题
  private async createContinuousStream1() {
    try {
      if (this.frameQueue.length > 0) {
        // 确保文件路径存在
        if (!this.tempFilePath) {
          await this.createYuvFilePath();
        }

        // 使用最新的帧
        const latestFrame = this.frameQueue[this.frameQueue.length - 1];
        const expectedSize = 1920 * 1080 * 1.5; // 3110400

        // 再次验证帧大小
        if (latestFrame.byteLength !== expectedSize) {
          console.log(`player_跳过不完整帧: ${latestFrame.byteLength} 字节`);
          return;
        }

        // 简化方案：减少重复次数，避免数据处理错误
        const frameView = new Uint8Array(latestFrame);

        // 创建少量重复帧（重复2次，避免复杂的数据处理）
        const repeatCount = 2;
        const totalSize = latestFrame.byteLength * repeatCount;
        const repeatedBuffer = new ArrayBuffer(totalSize);
        const repeatedView = new Uint8Array(repeatedBuffer);

        // 直接重复写入原始帧
        for (let i = 0; i < repeatCount; i++) {
          repeatedView.set(frameView, i * latestFrame.byteLength);
        }

        // 使用原子性文件操作避免竞态条件
        const writingFilePath = this.tempFilePath + '.writing';
        console.log('file__writingFilePath',writingFilePath)
        // 确保缓存目录存在
        // if (!fs.accessSync(writingFilePath)) {
        //   console.log('file_不存在writingFilePath')
        // }
        // 写入到临时文件
        const tempFile = fs.openSync(writingFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY | fs.OpenMode.TRUNC);
        fs.writeSync(tempFile.fd, repeatedBuffer);
        fs.fsyncSync(tempFile.fd);
        fs.closeSync(tempFile);
        // 读取临时文件内容
        const readFile = fs.openSync(writingFilePath, fs.OpenMode.READ_ONLY);
        const tempData = new ArrayBuffer(totalSize);
        fs.readSync(readFile.fd, tempData);
        fs.closeSync(readFile);

        // 原子性地覆盖目标文件（保持inode不变）
        const targetFile = fs.openSync(this.tempFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY | fs.OpenMode.TRUNC);
        fs.writeSync(targetFile.fd, tempData);
        fs.fsyncSync(targetFile.fd);
        fs.closeSync(targetFile);

        // 清理临时文件
        fs.unlinkSync(writingFilePath);
        // // 写入到临时文件
        // const file = fs.openSync(writingFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY | fs.OpenMode.TRUNC);
        // fs.writeSync(file.fd, repeatedBuffer);
        // // fs.fsyncSync(file.fd); // 强制刷新到磁盘
        // fs.closeSync(file);
        //
        // // 原子性重命名（关键操作）
        // fs.renameSync(writingFilePath, this.tempFilePath);
        console.log(`player_原子性更新循环视频: ${repeatCount} 帧, 帧大小: ${latestFrame.byteLength}, 总大小: ${totalSize} 字节`);
      }
    } catch (error) {
      console.log('player_原子性创建连续流失败:', error);
    }
  }

  // 清除流数据
  private clearStreamData() {
    this.hasStreamData = false;
    this.message = 'Stream Player';

    // 清理临时文件
    if (this.tempFilePath) {
      try {
        if (fs.accessSync(this.tempFilePath)) {
          fs.unlinkSync(this.tempFilePath);
          LogUtils.getInstance().LOGI(`Deleted temp file: ${this.tempFilePath}`);
        }
      } catch (error) {
        LogUtils.getInstance().LOGI(`Failed to delete temp file: ${error}`);
      }
      this.tempFilePath = '';
    }

    // 重置播放器
    if (this.mIjkMediaPlayer) {
      this.mIjkMediaPlayer.reset();
    }

    LogUtils.getInstance().LOGI("Stream data cleared");
  }
}